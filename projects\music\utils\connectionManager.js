/**
 * نظام إدارة الاتصالات الصوتية المحسن
 * يحسن استقرار الاتصالات ويقلل من انقطاعها
 */

class VoiceConnectionManager {
  constructor() {
    this.connections = new Map(); // تتبع الاتصالات النشطة
    this.reconnectQueue = new Map(); // طابور إعادة الاتصال
    this.retryAttempts = new Map(); // عدد المحاولات
    this.maxRetries = 3;
    this.baseReconnectDelay = 2000;
    this.isShuttingDown = false;
    
    console.log('🔗 VoiceConnectionManager initialized');
  }

  /**
   * ضمان وجود اتصال صوتي صحيح
   * @param {string} botId - معرف البوت
   * @param {Object} channel - القناة الصوتية
   * @param {Object} distube - كائن DisTube
   * @returns {Promise<Object|null>} الاتصال أو null
   */
  async ensureConnection(botId, channel, distube) {
    try {
      // التحقق من الاتصال الحالي
      const existingConnection = this.connections.get(botId);
      
      if (existingConnection && this.isConnectionHealthy(existingConnection)) {
        console.log(`✅ Using existing healthy connection for bot ${botId}`);
        return existingConnection;
      }

      // إنشاء اتصال جديد
      return await this.createConnection(botId, channel, distube);
      
    } catch (error) {
      console.error(`❌ Error ensuring connection for bot ${botId}:`, error.message);
      return null;
    }
  }

  /**
   * إنشاء اتصال صوتي جديد
   * @param {string} botId - معرف البوت
   * @param {Object} channel - القناة الصوتية
   * @param {Object} distube - كائن DisTube
   * @returns {Promise<Object|null>} الاتصال الجديد
   */
  async createConnection(botId, channel, distube) {
    try {
      // منع المحاولات المتعددة المتزامنة
      if (this.reconnectQueue.has(botId)) {
        console.log(`⏳ Already attempting to connect bot ${botId}, waiting...`);
        return await this.waitForConnection(botId);
      }

      this.reconnectQueue.set(botId, true);
      
      // التحقق من صلاحيات القناة
      if (!this.validateChannelPermissions(channel)) {
        throw new Error(`Missing permissions for channel ${channel.name}`);
      }

      console.log(`🔄 Creating new connection for bot ${botId} to ${channel.name}`);
      
      const connection = await distube.voices.join(channel);
      
      // حفظ الاتصال وإعداد المراقبة
      this.connections.set(botId, {
        connection,
        channelId: channel.id,
        channelName: channel.name,
        createdAt: Date.now(),
        lastHealthCheck: Date.now()
      });
      
      this.setupConnectionMonitoring(botId, connection);
      
      // إعادة تعيين عداد المحاولات
      this.retryAttempts.delete(botId);
      this.reconnectQueue.delete(botId);
      
      console.log(`✅ Successfully connected bot ${botId} to ${channel.name}`);
      return connection;
      
    } catch (error) {
      this.reconnectQueue.delete(botId);
      console.error(`❌ Failed to create connection for bot ${botId}:`, error.message);
      
      // محاولة إعادة الاتصال إذا لم نصل للحد الأقصى
      await this.scheduleReconnection(botId, channel, distube, error);
      return null;
    }
  }

  /**
   * إعداد مراقبة الاتصال
   * @param {string} botId - معرف البوت
   * @param {Object} connection - كائن الاتصال
   */
  setupConnectionMonitoring(botId, connection) {
    // مراقبة حالة الاتصال (بدون إضافة listeners متعددة)
    if (!connection._connectionMonitoringSetup) {
      connection.on('stateChange', (oldState, newState) => {
        console.log(`🔄 Bot ${botId} connection: ${oldState.status} → ${newState.status}`);
        
        if (newState.status === 'disconnected' || newState.status === 'destroyed') {
          this.handleConnectionLoss(botId);
        }
      });

      connection.on('error', (error) => {
        console.error(`❌ Connection error for bot ${botId}:`, error.message);
        this.handleConnectionError(botId, error);
      });
      
      connection._connectionMonitoringSetup = true;
    }
  }

  /**
   * التحقق من صحة الاتصال
   * @param {Object} connectionData - بيانات الاتصال
   * @returns {boolean} هل الاتصال صحي
   */
  isConnectionHealthy(connectionData) {
    if (!connectionData || !connectionData.connection) return false;
    
    const { connection } = connectionData;
    const healthyStates = ['ready', 'connecting'];
    
    return healthyStates.includes(connection.state?.status);
  }

  /**
   * التحقق من صلاحيات القناة
   * @param {Object} channel - القناة الصوتية
   * @returns {boolean} هل الصلاحيات متوفرة
   */
  validateChannelPermissions(channel) {
    if (!channel || !channel.permissionsFor) return false;
    
    const permissions = channel.permissionsFor(channel.guild.members.me);
    return permissions && permissions.has(['Connect', 'Speak']);
  }

  /**
   * معالجة فقدان الاتصال
   * @param {string} botId - معرف البوت
   */
  async handleConnectionLoss(botId) {
    if (this.isShuttingDown) return;
    
    console.log(`🔌 Connection lost for bot ${botId}, scheduling reconnection...`);
    
    // إزالة الاتصال المفقود
    this.connections.delete(botId);
    
    // جدولة إعادة الاتصال
    setTimeout(() => {
      this.attemptAutoReconnection(botId);
    }, 1000);
  }

  /**
   * محاولة إعادة الاتصال التلقائي
   * @param {string} botId - معرف البوت
   */
  async attemptAutoReconnection(botId) {
    try {
      // هذه الدالة ستستدعى من الكود الرئيسي
      // نحن فقط نسجل المحاولة هنا
      console.log(`🔄 Auto-reconnection triggered for bot ${botId}`);
      
    } catch (error) {
      console.error(`❌ Auto-reconnection failed for bot ${botId}:`, error.message);
    }
  }

  /**
   * جدولة إعادة الاتصال مع تأخير متزايد
   * @param {string} botId - معرف البوت
   * @param {Object} channel - القناة الصوتية
   * @param {Object} distube - كائن DisTube
   * @param {Error} lastError - آخر خطأ حدث
   */
  async scheduleReconnection(botId, channel, distube, lastError) {
    const attempts = this.retryAttempts.get(botId) || 0;
    
    if (attempts >= this.maxRetries) {
      console.error(`❌ Max reconnection attempts (${this.maxRetries}) reached for bot ${botId}`);
      this.retryAttempts.delete(botId);
      return;
    }

    // تأخير متزايد (exponential backoff)
    const delay = this.baseReconnectDelay * Math.pow(2, attempts);
    
    console.log(`⏳ Scheduling reconnection attempt ${attempts + 1}/${this.maxRetries} for bot ${botId} in ${delay}ms`);
    
    setTimeout(async () => {
      if (this.isShuttingDown) return;
      
      this.retryAttempts.set(botId, attempts + 1);
      await this.createConnection(botId, channel, distube);
    }, delay);
  }

  /**
   * انتظار اكتمال الاتصال الجاري
   * @param {string} botId - معرف البوت
   * @returns {Promise<Object|null>} الاتصال أو null
   */
  async waitForConnection(botId, timeout = 10000) {
    const startTime = Date.now();
    
    while (this.reconnectQueue.has(botId) && (Date.now() - startTime) < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return this.connections.get(botId)?.connection || null;
  }

  /**
   * معالجة خطأ الاتصال
   * @param {string} botId - معرف البوت
   * @param {Error} error - الخطأ
   */
  handleConnectionError(botId, error) {
    console.error(`🚨 Connection error for bot ${botId}: ${error.message}`);
    
    // إزالة الاتصال المعطل
    this.connections.delete(botId);
  }

  /**
   * تنظيف الاتصالات المنتهية
   */
  cleanup() {
    let cleaned = 0;
    
    for (const [botId, connectionData] of this.connections) {
      if (!this.isConnectionHealthy(connectionData)) {
        this.connections.delete(botId);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} unhealthy connections`);
    }
  }

  /**
   * الحصول على إحصائيات الاتصالات
   * @returns {Object} إحصائيات الاتصالات
   */
  getConnectionStats() {
    const activeConnections = Array.from(this.connections.values())
      .filter(data => this.isConnectionHealthy(data)).length;
    
    return {
      active: activeConnections,
      total: this.connections.size,
      reconnecting: this.reconnectQueue.size,
      retrying: this.retryAttempts.size
    };
  }

  /**
   * إيقاف المدير بأمان
   */
  shutdown() {
    console.log('🔌 Shutting down VoiceConnectionManager...');
    this.isShuttingDown = true;
    
    // تنظيف جميع الاتصالات
    this.connections.clear();
    this.reconnectQueue.clear();
    this.retryAttempts.clear();
  }
}

module.exports = VoiceConnectionManager;
