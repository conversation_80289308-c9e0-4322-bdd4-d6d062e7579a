/**
 * نظام معالجة الأخطاء المتقدم
 * يوفر استجابة سريعة ومعالجة ذكية للأخطاء
 */

class ErrorHandler {
  constructor() {
    this.errorCounts = new Map(); // تتبع تكرار الأخطاء
    this.errorThreshold = 5; // حد الأخطاء المسموح
    this.resetInterval = 300000; // إعادة تعيين كل 5 دقائق
    this.lastErrors = []; // آخر الأخطاء للتحليل
    this.maxErrorHistory = 50; // حد الأخطاء المحفوظة
    
    // الأخطاء الحرجة التي تتطلب تدخل فوري
    this.criticalErrors = new Set([
      'VOICE_CONNECTION_TIMEOUT',
      'VOICE_CONNECT_FAILED',
      'FFMPEG_NOT_FOUND',
      'ECONNRESET',
      'ENOTFOUND'
    ]);
    
    // أنماط الأخطاء المعروفة
    this.errorPatterns = {
      network: ['ECONNRESET', 'ENOTFOUND', 'ETIMEDOUT', 'ECONNREFUSED'],
      voice: ['VOICE_CONNECTION_TIMEOUT', 'VOICE_CONNECT_FAILED'],
      media: ['Video unavailable', 'Sign in to confirm', 'Private video'],
      platform: ['SOUNDCLOUD_PLUGIN_RESOLVE_ERROR', 'YOUTUBE_PARSE_ERROR']
    };
    
    console.log('🚨 ErrorHandler initialized');
    this.startErrorReset();
  }

  /**
   * معالجة خطأ رئيسية
   * @param {Error} error - الخطأ
   * @param {Object} context - سياق الخطأ
   * @returns {Promise<Object>} نتيجة المعالجة
   */
  async handleError(error, context = {}) {
    try {
      // تسجيل الخطأ
      const errorInfo = this.logError(error, context);
      
      // تتبع تكرار الأخطاء
      this.trackErrorFrequency(error, context);
      
      // تحديد نوع الخطأ ومعالجته
      const errorType = this.categorizeError(error);
      const response = await this.processError(error, errorType, context);
      
      // حفظ الخطأ في التاريخ
      this.addToErrorHistory(errorInfo);
      
      return response;
      
    } catch (handlingError) {
      console.error('❌ Error in error handler:', handlingError.message);
      return this.createGenericErrorResponse();
    }
  }

  /**
   * تسجيل الخطأ مع معلومات مفصلة
   * @param {Error} error - الخطأ
   * @param {Object} context - السياق
   * @returns {Object} معلومات الخطأ
   */
  logError(error, context) {
    const timestamp = new Date().toISOString();
    const errorInfo = {
      timestamp,
      message: error.message,
      code: error.code || error.name,
      stack: error.stack,
      context: {
        botId: context.botId || 'unknown',
        channelId: context.channelId || 'unknown',
        command: context.command || 'unknown',
        userId: context.userId || 'unknown'
      }
    };
    
    // تسجيل مختصر في الكونسول
    const botInfo = context.botId ? `[Bot: ${context.botId.slice(-6)}]` : '';
    const commandInfo = context.command ? `[Cmd: ${context.command}]` : '';
    
    console.error(`🚨 [${timestamp.slice(11, 19)}] ${botInfo} ${commandInfo} ${error.message}`);
    
    // تسجيل مفصل للأخطاء الحرجة
    if (this.criticalErrors.has(error.code)) {
      console.error(`🔥 CRITICAL ERROR DETAILS:`, errorInfo);
    }
    
    return errorInfo;
  }

  /**
   * تتبع تكرار الأخطاء
   * @param {Error} error - الخطأ
   * @param {Object} context - السياق
   */
  trackErrorFrequency(error, context) {
    const errorKey = `${error.code || error.name}_${context.botId || 'global'}`;
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);
    
    // تحذير إذا تكرر الخطأ كثيراً
    if (count >= this.errorThreshold) {
      console.warn(`⚠️ Error threshold reached for ${errorKey}: ${count + 1} occurrences`);
    }
  }

  /**
   * تصنيف نوع الخطأ
   * @param {Error} error - الخطأ
   * @returns {string} نوع الخطأ
   */
  categorizeError(error) {
    const errorCode = error.code || error.name;
    const errorMessage = error.message.toLowerCase();
    
    // فحص الأنماط المعروفة
    for (const [category, patterns] of Object.entries(this.errorPatterns)) {
      if (patterns.some(pattern => 
        errorCode === pattern || errorMessage.includes(pattern.toLowerCase())
      )) {
        return category;
      }
    }
    
    return 'unknown';
  }

  /**
   * معالجة الخطأ حسب النوع
   * @param {Error} error - الخطأ
   * @param {string} errorType - نوع الخطأ
   * @param {Object} context - السياق
   * @returns {Promise<Object>} استجابة المعالجة
   */
  async processError(error, errorType, context) {
    switch (errorType) {
      case 'network':
        return await this.handleNetworkError(error, context);
      
      case 'voice':
        return await this.handleVoiceError(error, context);
      
      case 'media':
        return await this.handleMediaError(error, context);
      
      case 'platform':
        return await this.handlePlatformError(error, context);
      
      default:
        return await this.handleGenericError(error, context);
    }
  }

  /**
   * معالجة أخطاء الشبكة
   * @param {Error} error - الخطأ
   * @param {Object} context - السياق
   * @returns {Promise<Object>} الاستجابة
   */
  async handleNetworkError(error, context) {
    console.log(`🌐 Handling network error: ${error.message}`);
    
    return {
      type: 'network',
      message: '❌ مشكلة في الاتصال بالإنترنت، يرجى المحاولة مرة أخرى',
      action: 'retry',
      retryDelay: 3000
    };
  }

  /**
   * معالجة أخطاء الصوت
   * @param {Error} error - الخطأ
   * @param {Object} context - السياق
   * @returns {Promise<Object>} الاستجابة
   */
  async handleVoiceError(error, context) {
    console.log(`🔊 Handling voice error: ${error.message}`);
    
    return {
      type: 'voice',
      message: '❌ مشكلة في الاتصال الصوتي، جاري إعادة المحاولة...',
      action: 'reconnect',
      retryDelay: 2000
    };
  }

  /**
   * معالجة أخطاء الوسائط
   * @param {Error} error - الخطأ
   * @param {Object} context - السياق
   * @returns {Promise<Object>} الاستجابة
   */
  async handleMediaError(error, context) {
    console.log(`🎵 Handling media error: ${error.message}`);
    
    let message = '❌ خطأ في تشغيل الوسائط';
    
    if (error.message.includes('Video unavailable')) {
      message = '❌ الفيديو غير متاح أو محذوف';
    } else if (error.message.includes('Sign in to confirm')) {
      message = '❌ الفيديو يتطلب تسجيل دخول أو مقيد بالعمر';
    } else if (error.message.includes('Private video')) {
      message = '❌ الفيديو خاص ولا يمكن تشغيله';
    }
    
    return {
      type: 'media',
      message,
      action: 'skip',
      retryDelay: 0
    };
  }

  /**
   * معالجة أخطاء المنصات
   * @param {Error} error - الخطأ
   * @param {Object} context - السياق
   * @returns {Promise<Object>} الاستجابة
   */
  async handlePlatformError(error, context) {
    console.log(`🎧 Handling platform error: ${error.message}`);
    
    let message = '❌ مشكلة في المنصة الموسيقية';
    
    if (error.code === 'SOUNDCLOUD_PLUGIN_RESOLVE_ERROR') {
      message = '❌ SoundCloud غير متاح مؤقتاً - جرب YouTube أو Spotify';
    } else if (error.code === 'YOUTUBE_PARSE_ERROR') {
      message = '❌ خطأ في معالجة رابط YouTube';
    }
    
    return {
      type: 'platform',
      message,
      action: 'alternative',
      retryDelay: 1000
    };
  }

  /**
   * معالجة الأخطاء العامة
   * @param {Error} error - الخطأ
   * @param {Object} context - السياق
   * @returns {Promise<Object>} الاستجابة
   */
  async handleGenericError(error, context) {
    console.log(`❓ Handling generic error: ${error.message}`);
    
    return {
      type: 'generic',
      message: '❌ حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى',
      action: 'log',
      retryDelay: 1000
    };
  }

  /**
   * إنشاء استجابة خطأ عامة
   * @returns {Object} الاستجابة
   */
  createGenericErrorResponse() {
    return {
      type: 'fallback',
      message: '❌ حدث خطأ في النظام',
      action: 'none',
      retryDelay: 0
    };
  }

  /**
   * إضافة خطأ إلى التاريخ
   * @param {Object} errorInfo - معلومات الخطأ
   */
  addToErrorHistory(errorInfo) {
    this.lastErrors.push(errorInfo);
    
    // الاحتفاظ بآخر 50 خطأ فقط
    if (this.lastErrors.length > this.maxErrorHistory) {
      this.lastErrors.shift();
    }
  }

  /**
   * الحصول على إحصائيات الأخطاء
   * @returns {Object} إحصائيات الأخطاء
   */
  getErrorStats() {
    const now = Date.now();
    const lastHour = now - (60 * 60 * 1000);
    
    const recentErrors = this.lastErrors.filter(error => 
      new Date(error.timestamp).getTime() > lastHour
    );
    
    const errorsByType = {};
    recentErrors.forEach(error => {
      const type = this.categorizeError({ code: error.code, message: error.message });
      errorsByType[type] = (errorsByType[type] || 0) + 1;
    });
    
    return {
      total: this.lastErrors.length,
      lastHour: recentErrors.length,
      byType: errorsByType,
      mostFrequent: this.getMostFrequentErrors()
    };
  }

  /**
   * الحصول على أكثر الأخطاء تكراراً
   * @returns {Array} قائمة الأخطاء الأكثر تكراراً
   */
  getMostFrequentErrors() {
    const sorted = Array.from(this.errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
    
    return sorted.map(([key, count]) => ({ error: key, count }));
  }

  /**
   * إعادة تعيين عدادات الأخطاء دورياً
   */
  startErrorReset() {
    setInterval(() => {
      const oldSize = this.errorCounts.size;
      this.errorCounts.clear();
      
      if (oldSize > 0) {
        console.log(`🔄 Error counters reset (${oldSize} entries cleared)`);
      }
    }, this.resetInterval);
  }

  /**
   * تنظيف تاريخ الأخطاء القديم
   */
  cleanupOldErrors() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 ساعة
    const initialLength = this.lastErrors.length;
    
    this.lastErrors = this.lastErrors.filter(error => 
      new Date(error.timestamp).getTime() > cutoff
    );
    
    const cleaned = initialLength - this.lastErrors.length;
    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} old error records`);
    }
  }
}

module.exports = ErrorHandler;
