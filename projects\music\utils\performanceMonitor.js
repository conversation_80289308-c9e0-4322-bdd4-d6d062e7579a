/**
 * نظام مراقبة الأداء في الوقت الفعلي
 * يوفر إحصائيات مفصلة عن أداء البوت
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      // إحصائيات الاتصالات
      connections: {
        active: 0,
        failed: 0,
        reconnections: 0,
        totalUptime: 0,
        averageLatency: 0
      },
      
      // إحصائيات الأوامر
      commands: {
        executed: 0,
        failed: 0,
        averageResponseTime: 0,
        slowCommands: [],
        commandStats: new Map()
      },
      
      // إحصائيات الذاكرة
      memory: {
        heapUsed: 0,
        heapTotal: 0,
        external: 0,
        cacheSize: 0,
        peakUsage: 0
      },
      
      // إحصائيات الموسيقى
      music: {
        songsPlayed: 0,
        playbackErrors: 0,
        averageQueueSize: 0,
        totalPlaytime: 0,
        platformStats: {
          youtube: 0,
          spotify: 0,
          deezer: 0,
          soundcloud: 0
        }
      },
      
      // إحصائيات الأخطاء
      errors: {
        total: 0,
        byType: new Map(),
        critical: 0,
        resolved: 0
      }
    };
    
    this.startTime = Date.now();
    this.lastUpdate = Date.now();
    this.isMonitoring = false;
    
    console.log('📊 PerformanceMonitor initialized');
  }

  /**
   * بدء مراقبة الأداء
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('🚀 Performance monitoring started');
    
    // تقرير مفصل كل 5 دقائق
    this.detailedReportInterval = setInterval(() => {
      this.displayDetailedReport();
    }, 300000);
    
    // تقرير مختصر كل دقيقة
    this.quickReportInterval = setInterval(() => {
      this.displayQuickReport();
    }, 60000);
    
    // تحديث الذاكرة كل 30 ثانية
    this.memoryUpdateInterval = setInterval(() => {
      this.updateMemoryMetrics();
    }, 30000);
    
    // تنظيف البيانات القديمة كل 10 دقائق
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldData();
    }, 600000);
  }

  /**
   * إيقاف المراقبة
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    clearInterval(this.detailedReportInterval);
    clearInterval(this.quickReportInterval);
    clearInterval(this.memoryUpdateInterval);
    clearInterval(this.cleanupInterval);
    
    console.log('⏹️ Performance monitoring stopped');
  }

  /**
   * تحديث إحصائيات الاتصالات
   * @param {string} type - نوع التحديث
   * @param {Object} data - البيانات الإضافية
   */
  updateConnectionMetrics(type, data = {}) {
    switch (type) {
      case 'connected':
        this.metrics.connections.active++;
        break;
      case 'disconnected':
        this.metrics.connections.active = Math.max(0, this.metrics.connections.active - 1);
        break;
      case 'failed':
        this.metrics.connections.failed++;
        break;
      case 'reconnected':
        this.metrics.connections.reconnections++;
        break;
      case 'latency':
        if (data.latency) {
          const current = this.metrics.connections.averageLatency;
          const total = this.metrics.connections.active;
          this.metrics.connections.averageLatency = 
            total > 0 ? (current * (total - 1) + data.latency) / total : data.latency;
        }
        break;
    }
    
    this.updateUptime();
  }

  /**
   * تحديث إحصائيات الأوامر
   * @param {string} commandName - اسم الأمر
   * @param {number} responseTime - وقت الاستجابة
   * @param {boolean} success - هل نجح الأمر
   */
  updateCommandMetrics(commandName, responseTime, success = true) {
    if (success) {
      this.metrics.commands.executed++;
    } else {
      this.metrics.commands.failed++;
    }
    
    // تحديث إحصائيات الأمر المحدد
    const commandStats = this.metrics.commands.commandStats.get(commandName) || {
      executed: 0,
      failed: 0,
      totalResponseTime: 0,
      averageResponseTime: 0
    };
    
    if (success) {
      commandStats.executed++;
      commandStats.totalResponseTime += responseTime;
      commandStats.averageResponseTime = commandStats.totalResponseTime / commandStats.executed;
    } else {
      commandStats.failed++;
    }
    
    this.metrics.commands.commandStats.set(commandName, commandStats);
    
    // حساب متوسط وقت الاستجابة العام
    const totalCommands = this.metrics.commands.executed + this.metrics.commands.failed;
    if (totalCommands > 0) {
      const currentAvg = this.metrics.commands.averageResponseTime;
      this.metrics.commands.averageResponseTime = 
        (currentAvg * (totalCommands - 1) + responseTime) / totalCommands;
    }
    
    // تتبع الأوامر البطيئة
    if (responseTime > 1000) { // أكثر من ثانية
      this.metrics.commands.slowCommands.push({
        command: commandName,
        responseTime,
        timestamp: Date.now()
      });
      
      // الاحتفاظ بآخر 20 أمر بطيء فقط
      if (this.metrics.commands.slowCommands.length > 20) {
        this.metrics.commands.slowCommands.shift();
      }
    }
  }

  /**
   * تحديث إحصائيات الذاكرة
   */
  updateMemoryMetrics() {
    const memUsage = process.memoryUsage();
    
    this.metrics.memory = {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      cacheSize: this.getCacheSize()
    };
    
    // تتبع أعلى استخدام للذاكرة
    if (this.metrics.memory.heapUsed > this.metrics.memory.peakUsage) {
      this.metrics.memory.peakUsage = this.metrics.memory.heapUsed;
    }
  }

  /**
   * تحديث إحصائيات الموسيقى
   * @param {string} platform - المنصة
   * @param {string} type - نوع التحديث
   * @param {Object} data - البيانات الإضافية
   */
  updateMusicMetrics(platform, type = 'played', data = {}) {
    switch (type) {
      case 'played':
        this.metrics.music.songsPlayed++;
        if (this.metrics.music.platformStats[platform] !== undefined) {
          this.metrics.music.platformStats[platform]++;
        }
        if (data.duration) {
          this.metrics.music.totalPlaytime += data.duration;
        }
        break;
      case 'error':
        this.metrics.music.playbackErrors++;
        break;
      case 'queue_size':
        if (data.size !== undefined) {
          // حساب متوسط حجم القائمة
          const current = this.metrics.music.averageQueueSize;
          const total = this.metrics.music.songsPlayed;
          this.metrics.music.averageQueueSize = 
            total > 0 ? (current * total + data.size) / (total + 1) : data.size;
        }
        break;
    }
  }

  /**
   * تحديث إحصائيات الأخطاء
   * @param {string} errorType - نوع الخطأ
   * @param {boolean} critical - هل الخطأ حرج
   * @param {boolean} resolved - هل تم حل الخطأ
   */
  updateErrorMetrics(errorType, critical = false, resolved = false) {
    this.metrics.errors.total++;
    
    const typeCount = this.metrics.errors.byType.get(errorType) || 0;
    this.metrics.errors.byType.set(errorType, typeCount + 1);
    
    if (critical) {
      this.metrics.errors.critical++;
    }
    
    if (resolved) {
      this.metrics.errors.resolved++;
    }
  }

  /**
   * تحديث وقت التشغيل
   */
  updateUptime() {
    this.metrics.connections.totalUptime = Date.now() - this.startTime;
  }

  /**
   * الحصول على حجم الـ Cache
   * @returns {number} حجم الـ Cache بالـ MB
   */
  getCacheSize() {
    try {
      const cacheManager = require('./cacheManager');
      const stats = cacheManager.getCacheStats();
      return Math.round(stats.total * 0.001); // تقدير تقريبي
    } catch (error) {
      return 0;
    }
  }

  /**
   * حساب معدل النجاح
   * @param {number} success - عدد النجاحات
   * @param {number} failed - عدد الفشل
   * @returns {number} معدل النجاح بالنسبة المئوية
   */
  calculateSuccessRate(success, failed) {
    const total = success + failed;
    return total > 0 ? Math.round((success / total) * 100) : 100;
  }

  /**
   * الحصول على تقرير الأداء الشامل
   * @returns {Object} تقرير الأداء
   */
  getPerformanceReport() {
    this.updateMemoryMetrics();
    
    const uptime = Date.now() - this.startTime;
    const uptimeHours = Math.round(uptime / 1000 / 60 / 60 * 100) / 100;
    
    return {
      timestamp: new Date().toISOString(),
      uptime: `${uptimeHours}h`,
      
      // معدلات النجاح
      successRates: {
        connections: this.calculateSuccessRate(
          this.metrics.connections.active + this.metrics.connections.reconnections,
          this.metrics.connections.failed
        ),
        commands: this.calculateSuccessRate(
          this.metrics.commands.executed,
          this.metrics.commands.failed
        ),
        music: this.calculateSuccessRate(
          this.metrics.music.songsPlayed,
          this.metrics.music.playbackErrors
        ),
        errorResolution: this.calculateSuccessRate(
          this.metrics.errors.resolved,
          this.metrics.errors.total - this.metrics.errors.resolved
        )
      },
      
      // الأداء
      performance: {
        averageCommandResponse: `${Math.round(this.metrics.commands.averageResponseTime)}ms`,
        memoryUsage: `${this.metrics.memory.heapUsed}MB / ${this.metrics.memory.heapTotal}MB`,
        peakMemory: `${this.metrics.memory.peakUsage}MB`,
        cacheSize: `${this.metrics.memory.cacheSize}MB`,
        averageLatency: `${Math.round(this.metrics.connections.averageLatency)}ms`
      },
      
      // الإحصائيات المفصلة
      detailed: this.metrics
    };
  }

  /**
   * عرض تقرير مفصل
   */
  displayDetailedReport() {
    const report = this.getPerformanceReport();
    
    console.log(`\n📊 ===== DETAILED PERFORMANCE REPORT =====`);
    console.log(`⏱️  Uptime: ${report.uptime}`);
    console.log(`🔗 Connections: ${report.successRates.connections}% success (${this.metrics.connections.active} active)`);
    console.log(`⚡ Commands: ${report.successRates.commands}% success (${this.metrics.commands.executed} executed)`);
    console.log(`🎵 Music: ${report.successRates.music}% success (${this.metrics.music.songsPlayed} played)`);
    console.log(`🚨 Errors: ${report.successRates.errorResolution}% resolved (${this.metrics.errors.total} total)`);
    console.log(`🚀 Performance: ${report.performance.averageCommandResponse} avg response`);
    console.log(`💾 Memory: ${report.performance.memoryUsage} (peak: ${report.performance.peakMemory})`);
    console.log(`📈 Cache: ${report.performance.cacheSize}`);
    
    // تحذيرات
    this.displayWarnings(report);
    
    console.log(`==========================================\n`);
  }

  /**
   * عرض تقرير سريع
   */
  displayQuickReport() {
    const report = this.getPerformanceReport();
    
    console.log(`📊 Quick Stats: ` +
      `Mem: ${report.performance.memoryUsage} | ` +
      `Cmds: ${this.metrics.commands.executed} | ` +
      `Music: ${this.metrics.music.songsPlayed} | ` +
      `Errors: ${this.metrics.errors.total}`);
  }

  /**
   * عرض التحذيرات
   * @param {Object} report - تقرير الأداء
   */
  displayWarnings(report) {
    const warnings = [];
    
    if (report.successRates.connections < 90) {
      warnings.push(`Low connection success rate: ${report.successRates.connections}%`);
    }
    
    if (this.metrics.memory.heapUsed > 200) {
      warnings.push(`High memory usage: ${this.metrics.memory.heapUsed}MB`);
    }
    
    if (this.metrics.commands.averageResponseTime > 500) {
      warnings.push(`Slow command response: ${Math.round(this.metrics.commands.averageResponseTime)}ms`);
    }
    
    if (this.metrics.errors.critical > 5) {
      warnings.push(`High critical errors: ${this.metrics.errors.critical}`);
    }
    
    if (warnings.length > 0) {
      console.log(`⚠️  WARNINGS:`);
      warnings.forEach(warning => console.log(`   - ${warning}`));
    }
  }

  /**
   * تنظيف البيانات القديمة
   */
  cleanupOldData() {
    // تنظيف الأوامر البطيئة القديمة (أكثر من ساعة)
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    this.metrics.commands.slowCommands = this.metrics.commands.slowCommands
      .filter(cmd => cmd.timestamp > oneHourAgo);
    
    console.log(`🧹 Performance data cleanup completed`);
  }

  /**
   * إعادة تعيين الإحصائيات
   */
  resetMetrics() {
    const oldMetrics = { ...this.metrics };
    
    // إعادة تعيين معظم الإحصائيات مع الاحتفاظ بالمهمة
    this.metrics.commands.executed = 0;
    this.metrics.commands.failed = 0;
    this.metrics.commands.slowCommands = [];
    this.metrics.music.songsPlayed = 0;
    this.metrics.music.playbackErrors = 0;
    this.metrics.errors.total = 0;
    this.metrics.errors.byType.clear();
    this.metrics.errors.critical = 0;
    this.metrics.errors.resolved = 0;
    
    console.log(`🔄 Performance metrics reset`);
    return oldMetrics;
  }
}

module.exports = PerformanceMonitor;
