const pm2 = require("pm2");
const Discord = require("discord.js");
const { MessageFlags } = require("discord.js");
const { GatewayIntentBits, Partials, ActivityType, EmbedBuilder } = require("discord.js");
const { joinVoiceChannel } = require("@discordjs/voice");

// تحميل متغيرات البيئة
require('dotenv').config();

const config = {
  owners: process.env.owner_id ? [process.env.owner_id] : []
};
const { DisTube } = require("distube");
const { DeezerPlugin } = require("@distube/deezer");
const { SpotifyPlugin } = require("@distube/spotify");
const { SoundCloudPlugin } = require("@distube/soundcloud");

// دالة للتحقق من وجود ffmpeg
function getFFmpegPath() {
  const { execSync } = require('child_process');
  const path = require('path');

  try {
    // محاولة استخدام ffmpeg من النظام
    execSync('ffmpeg -version', { stdio: 'ignore' });
    console.log('✅ Using system ffmpeg');
    return undefined; // استخدام ffmpeg من النظام
  } catch (error) {
    try {
      // محاولة استخدام ffmpeg-static
      const ffmpegStatic = require('ffmpeg-static');
      if (require('fs').existsSync(ffmpegStatic)) {
        console.log('✅ Using ffmpeg-static');
        return ffmpegStatic;
      }
    } catch (staticError) {
      console.error('❌ ffmpeg-static not found');
    }

    console.error('❌ ffmpeg not found in system PATH and ffmpeg-static failed');
    console.error('Please install ffmpeg or run install-ffmpeg.bat');
    return null;
  }
}

// استيراد خدمات MongoDB الجديدة
const { dbConnection } = require("./database/connection");
const musicBotService = require("./services/musicBotService");

// استيراد نظام الـ Cache والتحسينات الجديدة
const cacheManager = require('./utils/cacheManager');
const { startPerformanceMonitoring, getPerformanceStats } = require('./utils/performanceOptimizer');

// استيراد الأنظمة المحسنة الجديدة
const VoiceConnectionManager = require('./utils/connectionManager');
const ErrorHandler = require('./utils/errorHandler');
const PerformanceMonitor = require('./utils/performanceMonitor');

const fs = require('fs');
const path = require('path');
const axios = require("axios");

// تحميل نظام الأوامر الجديد
const { loadCommands } = require('./commands/commandLoader');
const { checkMusicPermissions, sendPermissionDeniedMessage } = require('./utils/musicPermissions');

// قاعدة بيانات الاشتراكات (ستبقى كما هي مؤقتاً)
const { Database } = require("st.db");
const sub_db = new Database("databases/subscriptions.json");

const defaultData = {
  volume: 100,
  repeat: false,
  channelId: null,
  activity: [{ type: ActivityType.Playing, name: null }],
  guildId: null,
  owners: [],
  commandsChat: null,
}

// دالة مساعدة لتحديث بيانات البوت في MongoDB
async function updateBotData(botId, data, description = "") {
  try {
    await musicBotService.set(botId, data);

    if (description) {
      console.log(`Updated bot data for ${botId} - ${description}`);
    }

    return true;
  } catch (error) {
    console.error(`Error updating bot data for ${botId}:`, error);
    return false;
  }
}
async function runBots() {
  try {
    // التحقق من وجود معرف المالك
    if (!process.env.owner_id) {
      console.error("Error: owner_id environment variable is not set");
      return process.exit(1);
    }

    // الحصول على بيانات الاشتراكات
    let sub_data = await sub_db.get(process.env.owner_id);
    if (!sub_data || !Array.isArray(sub_data) || sub_data.length === 0) {
      console.error(`No subscription data found for owner ID: ${process.env.owner_id}`);
      return process.exit(1);
    }

    // التحقق من وجود معرف الاشتراك
    if (!process.env.subscription_id) {
      console.error("Error: subscription_id environment variable is not set");
      return process.exit(1);
    }

    // البحث عن الاشتراك المطلوب
    let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
    if (!subscription) {
      console.error(`No subscription found with ID: ${process.env.subscription_id}`);
      return process.exit(1);
    }

    // تشغيل البوتات
    subscription.bots.forEach((token) => {
      runMusic(token.botToken);
    });
  } catch (error) {
    console.error("Error in runBots:", error);
    return process.exit(1);
  }
}
runBots();

function runMusic(token) {
  const bot_messages = new Map();

  // إنشاء الأنظمة المحسنة الجديدة
  const connectionManager = new VoiceConnectionManager();
  const errorHandler = new ErrorHandler();
  const performanceMonitor = new PerformanceMonitor();

  // نظام تنظيف الذاكرة المحسن
  const memoryManager = {
    maxMessages: 50, // تقليل من عدد غير محدود إلى 50 رسالة
    cleanupInterval: 60000, // كل دقيقة بدلاً من 30 دقيقة

    startCleanup() {
      setInterval(() => {
        const now = Date.now();
        let cleaned = 0;

        // تنظيف الرسائل القديمة (أكثر من 5 دقائق)
        for (const [messageId, msg] of bot_messages) {
          if (msg && msg.createdTimestamp && (now - msg.createdTimestamp > 300000)) {
            bot_messages.delete(messageId);
            cleaned++;
          }
        }

        // إذا تجاوز العدد الحد الأقصى، احذف الأقدم
        if (bot_messages.size > this.maxMessages) {
          const entries = Array.from(bot_messages.entries());
          const toDelete = entries.slice(0, bot_messages.size - this.maxMessages);
          toDelete.forEach(([id]) => bot_messages.delete(id));
          cleaned += toDelete.length;
        }

        if (cleaned > 0) {
          console.log(`🧹 Memory cleanup: removed ${cleaned} old messages for ${music_client.user?.tag}`);
        }

        // تنظيف الذاكرة إذا تجاوزت 100MB
        const memUsage = process.memoryUsage();
        if (memUsage.heapUsed > 100 * 1024 * 1024) {
          if (global.gc) {
            global.gc();
            console.log(`🗑️ Forced garbage collection for ${music_client.user?.tag}`);
          }
        }
      }, this.cleanupInterval);
    }
  };

  const music_client = new Discord.Client({
    partials: [
      Partials.User,
      Partials.GuildMember,
      Partials.Channel,
      Partials.Message
    ],
    intents: [
      GatewayIntentBits.Guilds,
      GatewayIntentBits.GuildMembers,
      GatewayIntentBits.GuildMessages,
      GatewayIntentBits.MessageContent,
      GatewayIntentBits.GuildVoiceStates
    ]
  });
  let channelId = null;
  let isReconnecting = false; // متغير لتتبع حالة إعادة الاتصال

  // تحميل الأوامر
  const commands = loadCommands(path.join(__dirname, 'commands'));
  console.log(`📦 تم تحميل ${commands.size} أمر للبوت ${music_client.user?.tag || 'Unknown'}`);

  // دالة مساعدة لإرسال الرسائل بأمان
  const safeSendMessage = (channel, options) => {
    // التحقق من صحة المحتوى قبل الإرسال
    if (!options || (!options.content && !options.embeds && !options.components && !options.files)) {
      console.error("Cannot send empty message - no content, embeds, components, or files provided");
      return Promise.resolve(null);
    }

    // التحقق من أن المحتوى ليس فارغاً
    if (options.content === undefined && (!options.embeds || options.embeds.length === 0)) {
      console.error("Cannot send message with undefined content and no embeds");
      return Promise.resolve(null);
    }

    return channel.send(options).catch(error => {
      console.error("Error sending message:", error);
      return null;
    });
  };

  // دالة مساعدة لمعالجة أخطاء "Unknown Message"
  const handleDiscordError = (error, context = "Discord operation") => {
    // تجاهل أخطاء "Unknown Message" لأن الرسالة قد تكون محذوفة بالفعل
    if (error.code !== 10008) {
      console.error(`Error in ${context}:`, error);
    }
  };

  // الحصول على مسار ffmpeg
  const ffmpegPath = getFFmpegPath();
  if (ffmpegPath === null) {
    console.error('❌ Cannot start bot without ffmpeg. Please install ffmpeg first.');
    return;
  }

  // إعداد DisTube مع ffmpeg
  const distubeOptions = {
    leaveOnStop: false,
    leaveOnFinish: false,
    leaveOnEmpty: false,
    emitNewSongOnly: true,
    nsfw: false,
    emitAddSongWhenCreatingQueue: true,
    emitAddListWhenCreatingQueue: true,
    searchSongs: 1,
    // إعدادات ytdl محسنة للصوت
    ytdlOptions: {
      quality: 'highestaudio',
      highWaterMark: 1 << 25,
      filter: 'audioonly',
      format: 'audioonly',
      requestOptions: {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      }
    },
    plugins: [
      new DeezerPlugin({
        emitEventsAfterFetching: true
      }),
      new SpotifyPlugin({
        emitEventsAfterFetching: true
      }),
      new SoundCloudPlugin()
    ]
  };

  // إضافة مسار ffmpeg إذا كان متوفراً
  if (ffmpegPath) {
    distubeOptions.ffmpeg = {
      path: ffmpegPath
    };
  }

  music_client.distube = new DisTube(music_client, distubeOptions);

  // إدارة Event Listeners بشكل صحيح لمنع التسريب
  const eventListenerManager = {
    listeners: new Map(),

    addListener(emitter, event, listener, name) {
      // إزالة المستمع السابق إذا وجد
      if (this.listeners.has(name)) {
        const oldListener = this.listeners.get(name);
        emitter.removeListener(event, oldListener);
      }

      // إضافة المستمع الجديد
      emitter.on(event, listener);
      this.listeners.set(name, listener);
    },

    removeAllListeners() {
      this.listeners.clear();
    }
  };

  // استخدام eventListenerManager لمنع تسريب الذاكرة
  eventListenerManager.addListener(
    music_client.distube,
    "disconnect",
    async (queue) => {
      console.log("DisTube disconnected from voice channel");

      // تسجيل انقطاع الاتصال في مراقب الأداء
      performanceMonitor.updateConnectionMetrics('disconnected');

      // تجنب إعادة الاتصال التلقائي هنا لأن voiceStateUpdate سيتولى الأمر
      // هذا يمنع التداخل والتكرار في محاولات الاتصال
      if (queue && queue.voiceChannel) {
        console.log(`Queue exists for voice channel: ${queue.voiceChannel.name}`);
        console.log("Voice reconnection will be handled by voiceStateUpdate event");
      }
    },
    "disconnect"
  );



  music_client.on("ready", async () => {
    try {
      console.log("Login Successfully:", music_client.user.tag);

      // بدء مراقبة الأداء للـ Cache
      startPerformanceMonitoring();
      console.log("🚀 Performance monitoring started for", music_client.user.tag);

      // انتظار قليل للتأكد من جاهزية الـ shards
      await new Promise(resolve => setTimeout(resolve, 3000));

      // التحقق من ملف إعادة التشغيل
      checkRestartMessage(music_client);

      // تنظيف bot_messages عند إعادة تشغيل البوت
      bot_messages.clear();
      console.log(`Cleared bot_messages cache for ${music_client.user.tag}`);

      // التحقق من وجود معرف المالك
      if (!process.env.owner_id) {
        console.error("Error: owner_id environment variable is not set");
        process.env.owner_id = "default_owner_id"; // استخدام قيمة افتراضية
        console.log("Using default owner_id:", process.env.owner_id);
      }

      // وضع علامة أن البوت جاهز
      music_client.isFullyReady = true;

      // بدء الأنظمة المحسنة
      memoryManager.startCleanup();
      performanceMonitor.startMonitoring();

      // تسجيل بدء البوت في مراقب الأداء
      performanceMonitor.updateConnectionMetrics('connected');

      // الحصول على بيانات الاشتراكات
      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data) || sub_data.length === 0) {
        console.error(`No subscription data found for owner ID: ${process.env.owner_id}`);
        // إنشاء بيانات افتراضية
        sub_data = [{ id: "default_subscription", guildID: music_client.guilds.cache.first()?.id }];
        console.log("Using default subscription data:", sub_data);
      }

      // التحقق من وجود معرف الاشتراك
      if (!process.env.subscription_id) {
        console.error("Error: subscription_id environment variable is not set");
        process.env.subscription_id = sub_data[0].id; // استخدام أول اشتراك متاح
        console.log("Using default subscription_id:", process.env.subscription_id);
      }

      // البحث عن الاشتراك المطلوب
      let subscription = sub_data.find(s => s.id == process.env.subscription_id);
      if (!subscription) {
        console.error(`No subscription found with ID: ${process.env.subscription_id}`);
        return process.exit(1);
      }

      // تعيين معرف السيرفر
      process.env.guild_id = subscription.guildID;

      // مغادرة السيرفرات الأخرى
      music_client.guilds.cache.filter(g => g.id != process.env.guild_id).forEach(guild => guild.leave());

      // الحصول على بيانات البوت من MongoDB
      let data = await musicBotService.get(music_client.user.id);
      channelId = data.commandsChat;

      // الانضمام إلى القناة الصوتية مع معالجة محسنة
      let channel = music_client.channels.cache.get(data.channelId);
      if (channel && channel.type === 2) { // التأكد من أنها قناة صوتية
        console.log(`Attempting to join voice channel: ${channel.id} - ${channel.name}`);

        try {
          // التحقق من الصلاحيات قبل الانضمام
          const permissions = channel.permissionsFor(music_client.user);
          if (!permissions.has(['Connect', 'Speak'])) {
            console.error(`Missing permissions for voice channel ${channel.name}. Required: Connect, Speak`);
            return;
          }

          // التحقق من حالة القناة
          if (channel.userLimit > 0 && channel.members.size >= channel.userLimit) {
            console.error(`Voice channel ${channel.name} is full (${channel.members.size}/${channel.userLimit})`);
            return;
          }

          // محاولة الانضمام مع timeout مخصص
          console.log("Joining voice channel...");
          const connection = await music_client.distube.voices.join(channel);
          console.log("Successfully joined voice channel");

        } catch (error) {
          console.error("Error joining voice channel:", error.message);

          // إذا كان الخطأ متعلق بالـ shard، انتظار وإعادة المحاولة
          if (error.message && error.message.includes("Shard")) {
            console.log("Shard error detected on startup, retrying in 3 seconds...");
            setTimeout(async () => {
              try {
                await music_client.distube.voices.join(channel);
                console.log("Successfully joined voice channel on shard retry");
              } catch (retryError) {
                console.error("Failed to join voice channel on shard retry:", retryError);
              }
            }, 3000);
          }
          // محاولة إعادة الاتصال مرة واحدة بعد 5 ثوانٍ للأخطاء الأخرى
          else if (error.code === 'VOICE_CONNECT_FAILED') {
            console.log("Retrying voice connection in 5 seconds...");
            setTimeout(async () => {
              try {
                await music_client.distube.voices.join(channel);
                console.log("Successfully joined voice channel on retry");
              } catch (retryError) {
                console.error("Failed to join voice channel on retry:", retryError.message);
                // تحديث قاعدة البيانات لإزالة القناة المعطلة
                try {
                  let data = await musicBotService.get(music_client.user.id);
                  data.channelId = null;
                  await updateBotData(music_client.user.id, data, "Removed invalid voice channel");
                  console.log("Removed invalid voice channel from database");
                } catch (dbError) {
                  console.error("Error updating database:", dbError);
                }
              }
            }, 5000);
          }
        }
      } else {
        if (!channel) {
          console.log(`Voice channel with ID ${data.channelId} not found - may have been deleted`);
          // إزالة القناة المحذوفة من قاعدة البيانات
          try {
            data.channelId = null;
            await updateBotData(music_client.user.id, data, "Removed deleted voice channel");
            console.log("Removed deleted voice channel from database");
          } catch (dbError) {
            console.error("Error updating database:", dbError);
          }
        } else {
          console.log(`Channel ${channel.name} is not a voice channel (type: ${channel.type})`);
        }
      }

      // تعيين حالة البوت
      if (data.game) {
        try {
          music_client.user.setPresence({
            activities: [{
              name: data.game,
              type: 0 // PLAYING
            }],
            status: 'online'
          });
          console.log(`Set presence for ${music_client.user.username}: ${data.game}`);
        } catch (error) {
          console.error("Error setting presence:", error);
        }
      } else if (data.activity?.name) {
        try {
          music_client.user.setPresence({
            activities: [data.activity]
          });
        } catch (error) {
          console.error("Error setting presence:", error);
        }
      }
    } catch (error) {
      console.error("Error in ready event:", error);
    }
  });

  music_client.on("guildCreate", async guild => {
    if (process.env.guild_id != guild.id) return guild.leave();
  });

  music_client.on("messageDelete", async message => {
    let msg = bot_messages.get(message.id);
    if (!msg) return;
    try {
      msg.delete().catch(error => {
        // تجاهل أخطاء "Unknown Message" لأن الرسالة قد تكون محذوفة بالفعل
        if (error.code !== 10008) {
          console.error("Error deleting message:", error);
        }
      });
    } catch (error) {
      console.error("Error in messageDelete event:", error);
    }
  });

  // معالج الأزرار التفاعلية
  music_client.on("interactionCreate", async (interaction) => {
    if (!interaction.isButton()) return;

    // قائمة الأزرار المتعلقة بالموسيقى فقط
    const musicButtons = [
      "repeat", "volume_down", "pause_resume", "volume_up", "skip",
      "previous", "stop", "shuffle", "queue_list"
    ];

    // استثناء الأزرار غير المتعلقة بالموسيقى
    const nonMusicButtons = [
      "confirm_restart", "cancel_restart", "confirm_leave", "cancel_leave",
      "0", "1", "2", "3", "4" // أزرار setgame
    ];

    // إذا كان الزر غير متعلق بالموسيقى، تجاهل التحقق من وجود قائمة تشغيل
    if (nonMusicButtons.includes(interaction.customId) ||
        interaction.customId.startsWith("confirm_") ||
        interaction.customId.startsWith("cancel_") ||
        interaction.customId.startsWith("panel_") ||
        interaction.customId.startsWith("purchase_")) {
      // هذه الأزرار لها معالجات خاصة بها في مكان آخر
      return;
    }

    // التحقق من أن الزر متعلق بالموسيقى
    if (!musicButtons.includes(interaction.customId)) {
      // إذا لم يكن الزر في قائمة أزرار الموسيقى، تجاهله
      return;
    }

    // الحصول على قائمة التشغيل الحالية (فقط للأزرار المتعلقة بالموسيقى)
    const queue = music_client.distube.getQueue(interaction.guild);
    if (!queue) {
      // استخدام نظام الردود الجديد مع الايقونات
      const { createMusicResponse } = require("./utils/responseHelper");

      const response = await createMusicResponse(musicBotService, music_client.user.id, {
        description: `:no_entry_sign: There must be music playing to use that!`,
        color: "#ff0000"
      }, 'nowplaying', interaction, music_client);

      // تحويل الرد إلى ephemeral
      response.flags = Discord.MessageFlags.Ephemeral;

      return interaction.reply(response);
    }

    // التحقق من أن المستخدم في نفس الروم الصوتي
    if (!interaction.member.voice.channelId) {
      return interaction.reply({
        content: `:no_entry_sign: You must join a voice channel to use that!`,
        flags: Discord.MessageFlags.Ephemeral
      });
    }

    const client_m = interaction.guild.members.me;
    if (client_m.voice.channelId && interaction.member.voice.channelId !== client_m.voice.channelId) {
      return interaction.reply({
        content: `:no_entry_sign: You must be listening in **${client_m.voice.channel.name}** to use that!`,
        flags: Discord.MessageFlags.Ephemeral
      });
    }

    // الحصول على بيانات البوت من الـ Cache (محسن للأداء)
    let data = await cacheManager.getBotData(music_client.user.id, musicBotService);

    // الحصول على نوع الرد واللون المخصص من قاعدة البيانات
    const responseType = data.responseType || 'embed';
    const embedColor = data.embedColor || '#36393f';

    // دالة مساعدة لإنشاء الردود مع نظام الايقونات
    const createButtonResponse = async (content, title = null, commandName = 'nowplaying') => {
      const { createMusicResponse } = require("./utils/responseHelper");

      const options = {
        description: content,
        color: embedColor
      };

      if (title) {
        options.title = title;
      }

      const response = await createMusicResponse(musicBotService, music_client.user.id, options, commandName, interaction, music_client, queue);

      // تحويل الرد إلى ephemeral
      response.flags = Discord.MessageFlags.Ephemeral;

      return response;
    };

    // معالجة الأزرار المختلفة
    switch (interaction.customId) {
      case "repeat":
        // تبديل وضع التكرار
        let repeat = data.repeat ? false : true;
        data.repeat = repeat;

        // تحديث قاعدة البيانات
        await updateBotData(music_client.user.id, data, "Set repeat mode");

        try {
          queue.setRepeatMode(repeat ? 1 : 0);
        } catch { }

        const repeatResponse = await createButtonResponse(`Repeat Mode: **${data.repeat ? "ON" : "OFF"}**`);
        await interaction.reply(repeatResponse);
        break;

      case "volume_down":
        // خفض مستوى الصوت بمقدار 10
        let newVolDown = Math.max(0, data.volume - 10);
        let oldVolDown = data.volume;
        data.volume = newVolDown;

        // تحديث قاعدة البيانات
        await updateBotData(music_client.user.id, data, "Volume down");

        // تغيير مستوى الصوت بشكل تدريجي
        await changeVolumeGradually(queue, oldVolDown, newVolDown);

        const volDownResponse = await createButtonResponse(`**Change Volume From \`${oldVolDown}\` To \`${newVolDown}\`**`, "Volume Level", "volume");
        await interaction.reply(volDownResponse);
        break;

      case "pause_resume":
        // تبديل حالة التشغيل/الإيقاف المؤقت
        if (queue.paused) {
          try {
            queue.resume();
          } catch { }
          const resumeResponse = await createButtonResponse(`**Resumed
[${queue.songs[0].name}](${queue.songs[0].url})**`, null, "resume");
          await interaction.reply(resumeResponse);
        } else {
          try {
            queue.pause();
          } catch { }
          const pauseResponse = await createButtonResponse(`**Paused
[${queue.songs[0].name}](${queue.songs[0].url})**`, null, "pause");
          await interaction.reply(pauseResponse);
        }
        break;

      case "volume_up":
        // رفع مستوى الصوت بمقدار 10
        let newVolUp = Math.min(150, data.volume + 10);
        let oldVolUp = data.volume;
        data.volume = newVolUp;

        // تحديث قاعدة البيانات
        await updateBotData(music_client.user.id, data, "Volume up");

        // تغيير مستوى الصوت بشكل تدريجي
        await changeVolumeGradually(queue, oldVolUp, newVolUp);

        const volUpResponse = await createButtonResponse(`**Change Volume From \`${oldVolUp}\` To \`${newVolUp}\`**`, "Volume Level", "volume");
        await interaction.reply(volUpResponse);
        break;

      case "skip":
        // تخطي الأغنية الحالية
        try {
          if (queue.songs.length > 1) {
            queue.skip();
          } else {
            queue.stop();
          }
        } catch { }

        const skipResponse = await createButtonResponse(`${queue.songs[0].name}`, "Skip Playback", "skip");

        await interaction.reply(skipResponse);
        break;

      default:
        const unknownResponse = await createButtonResponse("Unknown button action", null, "nowplaying");
        await interaction.reply(unknownResponse);
    }
  });

  music_client.on("messageCreate", async message => {
    if (!message.guild || message.author.bot) return;

    // إعادة تحميل بيانات البوت في كل رسالة للتأكد من التحديثات
    let data = await musicBotService.get(music_client.user.id);
    // تحديث channelId من قاعدة البيانات
    channelId = data.commandsChat;

    let args = message.content.split(" ");
    // أوامر خاصة (تتطلب منشن)
    if (message.mentions.users.has(music_client.user.id) && args.includes("settings")) {
      const settingsCommand = commands.get('settings');
      if (settingsCommand) {
        await settingsCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config);
      } else {
        console.error('Settings command not found in commands map');
      }
    }
    else if (message.mentions.users.has(music_client.user.id) && args.includes("mybots")) {
      const mybotsCommand = commands.get('mybots');
      if (mybotsCommand) {
        await mybotsCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, sub_db);
      } else {
        console.error('Mybots command not found in commands map');
      }
    }
    // أوامر الإدارة
    else if (args[0] === "comeall") {
      const comeallCommand = commands.get('comeall');
      if (comeallCommand) {
        await comeallCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, defaultData, updateBotData);
      } else {
        console.error('Comeall command not found in commands map');
      }
    }
    else if (args[0] === "avatarall") {
      const avataralCommand = commands.get('avatarall');
      if (avataralCommand) {
        await avataralCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, defaultData);
      } else {
        console.error('Avatarall command not found in commands map');
      }
    }
    else if (args[0] === "nameall") {
      const nameallCommand = commands.get('nameall');
      if (nameallCommand) {
        await nameallCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, defaultData);
      } else {
        console.error('Nameall command not found in commands map');
      }
    }
    else if (args[0] === "setchat") {
      const setchatCommand = commands.get('setchat');
      if (setchatCommand) {
        // دالة لتحديث channelId العام
        const setChannelId = (newChannelId) => { channelId = newChannelId; };
        await setchatCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, updateBotData, setChannelId);
      } else {
        console.error('Setchat command not found in commands map');
      }
    }
    else if (args[0] === "dischat") {
      const dischatCommand = commands.get('dischat');
      if (dischatCommand) {
        // دالة لتحديث channelId العام
        const setChannelId = (newChannelId) => { channelId = newChannelId; };
        await dischatCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, updateBotData, setChannelId);
      } else {
        console.error('Dischat command not found in commands map');
      }
    }
    // أوامر صلاحيات الموسيقى
    else if (args[0] === "allow") {
      const allowCommand = commands.get('allow');
      if (allowCommand) {
        await allowCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, updateBotData);
      } else {
        console.error('Allow command not found in commands map');
      }
    }
    else if (args[0] === "deny" || args[0] === "منع" || args[0] === "امنع" || args[0] === "block" || args[0] === "حظر") {
      const denyCommand = commands.get('deny');
      if (denyCommand) {
        await denyCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, updateBotData);
      } else {
        console.error('Deny command not found in commands map');
      }
    }
    else if (args[0] === "list" || args[0] === "قائمة" || args[0] === "permissions" || args[0] === "صلاحيات") {
      const listCommand = commands.get('list');
      if (listCommand) {
        await listCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config);
      } else {
        console.error('List command not found in commands map');
      }
    }
  });

  music_client.on("messageCreate", async message => {
    if (!message.guild || message.author.bot) return;
    let args = message.content.split(" ");
    let prefix;
    if (args[0] === `<@${music_client.user.id}>` || args[0] === `<@!${music_client.user.id}>` || args[0] === `${music_client.user.id}`) {
      prefix = "";
      args = args.slice(1);
    } else if (message.member.voice.channelId && message.member.voice.channelId === message.guild.members.me.voice.channelId) {
      prefix = "";
    } else {
      return;
    }

    // الحصول على بيانات البوت مرة واحدة لجميع الأوامر
    let data = await musicBotService.get(music_client.user.id);

    // التحقق من الأوامر الجديدة (نظام منفصل)
    if (args[0] === prefix + "play" || args[0] === prefix + "P" || args[0] === prefix + "p" || args[0] === prefix + "Play" || args[0] === prefix + "ش" || args[0] === prefix + "شغل"|| args[0] === prefix + "تشغيل") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const playCommand = commands.get('play');
      if (playCommand) {
        const startTime = Date.now();
        try {
          await playCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId);
          performanceMonitor.updateCommandMetrics('play', Date.now() - startTime, true);
        } catch (error) {
          performanceMonitor.updateCommandMetrics('play', Date.now() - startTime, false);
          await errorHandler.handleError(error, {
            botId: music_client.user.id,
            channelId: message.channel.id,
            command: 'play',
            userId: message.author.id
          });
        }
      } else {
        console.error('Play command not found in commands map');
      }
    }
    else if (args[0] === prefix + "stop" || args[0] === prefix + "وقف" || args[0] === prefix + "Stop" || args[0] === prefix + "st"|| args[0] === prefix + "St"|| args[0] === prefix + "ايقاف") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const stopCommand = commands.get('stop');
      if (stopCommand) {
        await stopCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId);
      } else {
        console.error('Stop command not found in commands map');
      }
    }
    else if (args[0] === prefix + "skip" || args[0] === prefix + "s" || args[0] === prefix + "Skip" || args[0] === prefix + "تخطي" || args[0] === prefix + "تخطى"|| args[0] === prefix + "S"|| args[0] === prefix + "س") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const skipCommand = commands.get('skip');
      if (skipCommand) {
        await skipCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId);
      } else {
        console.error('Skip command not found in commands map');
      }
    }
    else if (args[0] === prefix + "pause" || args[0] === prefix + "Pause" || args[0] === prefix + "pa" || args[0] === prefix + "توقف"|| args[0] === prefix + "Pa") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const pauseCommand = commands.get('pause');
      if (pauseCommand) {
        await pauseCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId);
      } else {
        console.error('Pause command not found in commands map');
      }
    }
    else if (args[0] === prefix + "resume" || args[0] === prefix + "Resume" || args[0] === prefix + "كمل" || args[0] === prefix + "استكمال") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const resumeCommand = commands.get('resume');
      if (resumeCommand) {
        await resumeCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId);
      } else {
        console.error('Resume command not found in commands map');
      }
    }
    else if (args[0] === prefix + "nowplaying" || args[0] === prefix + "np" || args[0] === prefix + "الان" || args[0] === prefix + "current") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const nowplayingCommand = commands.get('nowplaying');
      if (nowplayingCommand) {
        await nowplayingCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId);
      } else {
        console.error('Nowplaying command not found in commands map');
      }
    }
    else if (args[0] === prefix + "queue" || args[0] === prefix + "List" || args[0] === prefix + "Queue" || args[0] === prefix + "Qu" || args[0] === prefix + "qu" || args[0] === prefix + "list" || args[0] === prefix + "قائمة") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const queueCommand = commands.get('queue');
      if (queueCommand) {
        await queueCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId);
      } else {
        console.error('Queue command not found in commands map');
      }
    }
    else if (args[0] === prefix + "volume" || args[0] === prefix + "Volume" || args[0] === prefix + "V" || args[0] === prefix + "v" || args[0] === prefix + "Vol" || args[0] === prefix + "vol" || args[0] === prefix + "صوت" || args[0] === prefix + "ص") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const volumeCommand = commands.get('volume');
      if (volumeCommand) {
        await volumeCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, updateBotData);
      } else {
        console.error('Volume command not found in commands map');
      }
    }
    else if (args[0] === prefix + "repeat" || args[0] === prefix + "Repeat" || args[0] === prefix + "Loop" || args[0] === prefix + "كرر" || args[0] === prefix + "loop" || args[0] === prefix + "تكرار") {
      // التحقق من صلاحيات الموسيقى
      if (!checkMusicPermissions(message, data, config)) {
        return await sendPermissionDeniedMessage(message, bot_messages, musicBotService, music_client.user.id);
      }

      const repeatCommand = commands.get('repeat');
      if (repeatCommand) {
        await repeatCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, updateBotData);
      } else {
        console.error('Repeat command not found in commands map');
      }
    }
















    // الأوامر العامة
    else if (args[0] === prefix + "help" || args[0] === prefix + "مساعدة" || args[0] === prefix + "commands" || args[0] === prefix + "اوامر") {
      const helpCommand = commands.get('help');
      if (helpCommand) {
        await helpCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId);
      } else {
        console.error('Help command not found in commands map');
      }
    }
    else if (args[0] === prefix + "restart") {
      const restartCommand = commands.get('restart');
      if (restartCommand) {
        await restartCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config);
      } else {
        console.error('Restart command not found in commands map');
      }
    }
    // أوامر البوت الشخصية
    else if (args[0] === prefix + "setname") {
      const setnameCommand = commands.get('setname');
      if (setnameCommand) {
        await setnameCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config);
      } else {
        console.error('Setname command not found in commands map');
      }
    }
    else if (args[0] === prefix + "setavatar") {
      const setavatarCommand = commands.get('setavatar');
      if (setavatarCommand) {
        await setavatarCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, defaultData);
      } else {
        console.error('Setavatar command not found in commands map');
      }
    }
    else if (args[0] === prefix + "setgame") {
      const setgameCommand = commands.get('setgame');
      if (setgameCommand) {
        await setgameCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, defaultData, updateBotData);
      } else {
        console.error('Setgame command not found in commands map');
      }
    }
    else if (args[0] === prefix + "setup") {
      const setupCommand = commands.get('setup');
      if (setupCommand) {
        await setupCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, defaultData, updateBotData);
      } else {
        console.error('Setup command not found in commands map');
      }
    }
    else if (args[0] === prefix + "come") {
      const comeCommand = commands.get('come');
      if (comeCommand) {
        await comeCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, updateBotData);
      } else {
        console.error('Come command not found in commands map');
      }
    }
    else if (args[0] === prefix + "leave" || args[0] === prefix + "خروج" || args[0] === prefix + "disconnect" || args[0] === prefix + "اخرج" || args[0] === prefix + "انصراف") {
      const leaveCommand = commands.get('leave');
      if (leaveCommand) {
        await leaveCommand.execute(message, args, music_client, musicBotService, bot_messages, safeSendMessage, channelId, config, updateBotData);
      } else {
        console.error('Leave command not found in commands map');
      }
    }
  });

  music_client.on("voiceStateUpdate", async (oldState, newState) => {
    // التحقق من أن التحديث خاص بالبوت نفسه
    if (newState.id != music_client.user.id) return;

    try {
      let data = await musicBotService.get(music_client.user.id);
      if (!data || !data.channelId) return;

      // إذا تم فصل البوت من أي قناة صوتية، استخدام النظام المحسن لإعادة الاتصال
      if (!newState.channelId && oldState.channelId && !isReconnecting) {
        console.log(`🔄 Bot was disconnected from voice channel, using enhanced reconnection...`);

        // تسجيل انقطاع الاتصال في مراقب الأداء
        performanceMonitor.updateConnectionMetrics('disconnected');

        let channel = music_client.channels.cache.get(data.channelId);
        if (!channel) {
          console.log(`Target voice channel ${data.channelId} not found`);
          return;
        }

        isReconnecting = true;

        setTimeout(async () => {
          try {
            // التحقق من حالة البوت قبل إعادة الانضمام
            const currentState = music_client.guilds.cache.get(newState.guild.id)?.members.cache.get(music_client.user.id)?.voice;
            if (currentState?.channelId === data.channelId) {
              console.log("✅ Bot already rejoined the voice channel");
              isReconnecting = false;
              return;
            }

            // استخدام النظام المحسن لإعادة الاتصال
            const connection = await connectionManager.ensureConnection(
              music_client.user.id,
              channel,
              music_client.distube
            );

            if (connection) {
              console.log(`✅ Successfully rejoined voice channel using enhanced system: ${channel.name}`);
              performanceMonitor.updateConnectionMetrics('reconnected');
            } else {
              console.log(`⚠️ Enhanced reconnection returned null for ${channel.name}`);
            }

            isReconnecting = false;

          } catch (error) {
            console.log(`⚠️ Enhanced reconnection failed: ${error.message}`);

            // معالجة الخطأ باستخدام النظام المحسن
            await errorHandler.handleError(error, {
              botId: music_client.user.id,
              channelId: channel.id,
              context: 'voice_reconnection_fallback'
            });

            isReconnecting = false;
          }
        }, 2000);
      } else if (isReconnecting) {
        console.log("Already attempting to reconnect, skipping...");
      }

    } catch (error) {
      console.error("Error in voiceStateUpdate event:", error);
    }
  });





  music_client.distube
    .on("playSong", async (queue, song) => {
      console.log(`🎵 Now playing: ${song.name} in ${queue.voiceChannel.name}`);
      console.log(`🔊 Volume: ${queue.volume}%`);
      console.log(`👥 Voice channel members: ${queue.voiceChannel.members.size}`);

      // تسجيل تشغيل الأغنية في مراقب الأداء
      const platform = song.source || 'youtube';
      performanceMonitor.updateMusicMetrics(platform, 'played', {
        duration: song.duration,
        size: queue.songs.length
      });
    })
    .on("addSong", async (queue, song) => {
    // إذا كانت القائمة فارغة (أول أغنية)، نعرض رسالة "Now Playing"
    if (queue.songs.length <= 1) {

      // استخدام نظام الردود الجديد مع الايقونات
      const { createMusicResponse } = require("./utils/responseHelper");

      const response = await createMusicResponse(musicBotService, music_client.user.id, {
        title: "Now Listening",
        description: `**\n[${song.name}](${song.url})\n\nDuration \`${song.formattedDuration}\`**`
      }, 'play', null, music_client, queue);

      let messageContent;
      if (response.content) {
        // رد نصي
        messageContent = { content: response.content };
      } else {
        // رد Embed
        messageContent = { embeds: response.embeds };
      }

      // إضافة الأزرار (تعمل مع كلا النوعين)
      const row = new Discord.ActionRowBuilder()
        .addComponents(
          new Discord.ButtonBuilder()
            .setCustomId("repeat")
            .setEmoji("1376828504812949554")
            .setStyle(Discord.ButtonStyle.Secondary),
          new Discord.ButtonBuilder()
            .setCustomId("volume_down")
            .setEmoji("1376829683387338782")
            .setStyle(Discord.ButtonStyle.Secondary),
          new Discord.ButtonBuilder()
            .setCustomId("pause_resume")
            .setEmoji("1376827920294613062")
            .setStyle(Discord.ButtonStyle.Secondary),
          new Discord.ButtonBuilder()
            .setCustomId("volume_up")
            .setEmoji("1376829324056854528")
            .setStyle(Discord.ButtonStyle.Secondary),
          new Discord.ButtonBuilder()
            .setCustomId("skip")
            .setEmoji("1376828270968049775")
            .setStyle(Discord.ButtonStyle.Secondary)
        );

      messageContent.components = [row];

      await safeSendMessage(queue.textChannel, messageContent);
    } else {
      // إذا كانت هناك أغاني أخرى في القائمة، نعرض رسالة "Added to queue"
      const position = queue.songs.length - 1;
      const estimatedTime = prettyMilliseconds(queue.duration * 1000 - song.duration * 1000);

      // استخدام نظام الردود الجديد مع الايقونات
      const { createMusicResponse } = require("./utils/responseHelper");

      const response = await createMusicResponse(musicBotService, music_client.user.id, {
        title: "Added To List",
        description: `**[${song.name}](${song.url})\n\nDuration \`${song.formattedDuration}\`\nAdded to the queue at position ٫ \`#${position}\`\nEstimated time until playing ٫ \`${estimatedTime}\`**`
      }, 'play', null, music_client, queue);

      let messageContent;
      if (response.content) {
        // رد نصي
        messageContent = { content: response.content };
      } else {
        // رد Embed
        messageContent = { embeds: response.embeds };
      }

      await safeSendMessage(queue.textChannel, messageContent);
    }
  })
    .on("addList", async (queue, playlist) => {
      // استخدام نظام الردود الجديد مع الايقونات
      const { createMusicResponse } = require("./utils/responseHelper");

      const response = await createMusicResponse(musicBotService, music_client.user.id, {
        title: playlist.name,
        description: `Added to the queue at position #1\nEstimated time until playing: ${prettyMilliseconds(queue.duration * 1000)}`
      }, 'play', null, music_client, queue);

      let messageContent;
      if (response.content) {
        // رد نصي
        messageContent = { content: response.content };
      } else {
        // رد Embed
        messageContent = { embeds: response.embeds };
      }

      await safeSendMessage(queue.textChannel, messageContent);
    })
    .on("searchNoResult", async (message, _query) => {
      // استخدام نظام الردود الجديد مع الايقونات
      const { createMusicResponse } = require("./utils/responseHelper");

      const response = await createMusicResponse(musicBotService, music_client.user.id, {
        description: `🔍 **Not found.**`,
        color: "#ff0000",
      }, 'play', null, music_client);

      message.reply(response).then(msg => {
        bot_messages.set(message.id, msg);
        setTimeout(() => bot_messages.delete(message.id), 300000); // تقليل من 30 دقيقة إلى 5 دقائق
      }).catch(error => {
        // تجاهل أخطاء "Unknown Message" لأن الرسالة قد تكون محذوفة
        if (error.code !== 10008) {
          console.error("Error replying to search no result:", error);
        }
      });
    })
    .on("error", async (channel, error) => {
      // استخدام معالج الأخطاء المحسن
      const errorResponse = await errorHandler.handleError(error, {
        botId: music_client.user.id,
        channelId: channel?.id,
        context: 'distube_playback'
      });

      // تسجيل الخطأ في مراقب الأداء
      performanceMonitor.updateErrorMetrics(
        errorResponse.type,
        errorResponse.type === 'voice' || errorResponse.type === 'network',
        errorResponse.action === 'retry'
      );

      // إرسال رسالة خطأ للمستخدم
      if (channel && channel.send) {
        try {
          // استخدام نظام الردود الجديد مع الايقونات
          const { createMusicResponse } = require("./utils/responseHelper");

          const response = await createMusicResponse(musicBotService, music_client.user.id, {
            title: "خطأ في التشغيل",
            description: errorResponse.message,
            color: "#ff0000",
            footer: { text: "يرجى المحاولة مرة أخرى أو اختيار أغنية أخرى" }
          }, 'play', null, music_client);

          await safeSendMessage(channel, response);

          // تنفيذ الإجراء المقترح إذا لزم الأمر
          if (errorResponse.action === 'retry' && errorResponse.retryDelay > 0) {
            setTimeout(() => {
              console.log(`🔄 Auto-retry scheduled after ${errorResponse.retryDelay}ms for ${errorResponse.type} error`);
            }, errorResponse.retryDelay);
          }

        } catch (sendError) {
          console.error("Failed to send enhanced error message:", sendError.message);
        }
      }
    })
    // تم إزالة .on("disconnect") المكرر لتجنب MaxListenersExceededWarning
    // المعالجة تتم في eventListenerManager أعلاه
    .on("initQueue", async (queue) => {
      // إعدادات القائمة الافتراضية
      queue.autoplay = false;

      // الحصول على مستوى الصوت من قاعدة البيانات
      try {
        let data = await musicBotService.get(music_client.user.id);
        if (data && data.volume) {
          queue.volume = data.volume;
          console.log(`Queue initialized with volume: ${queue.volume}`);
        } else {
          queue.volume = 100;
          console.log("Queue initialized with default volume: 100");
        }
      } catch (error) {
        console.error("Error getting bot data for volume:", error);
        queue.volume = 100; // مستوى صوت افتراضي أعلى
        console.log("Queue initialized with fallback volume: 100");
      }

      // التأكد من أن البوت متصل بالقناة الصوتية
      if (queue.voiceChannel) {
        console.log(`Queue initialized for voice channel: ${queue.voiceChannel.name}`);
      } else {
        console.log("Warning: Queue initialized without voice channel");
      }
    });

  music_client.login(token).catch(() => console.log("Invalid token:", token));
}

// دالة للتحقق من ملف إعادة التشغيل وتحديث الرسالة
async function checkRestartMessage(music_client) {
  try {
    const fs = require('fs');
    const path = require('path');
    const restartFile = path.join(__dirname, 'temp_restart_info.json');

    // التحقق من وجود الملف
    if (!fs.existsSync(restartFile)) {
      return; // لا يوجد ملف إعادة تشغيل
    }

    // قراءة معلومات إعادة التشغيل
    const restartInfo = JSON.parse(fs.readFileSync(restartFile, 'utf8'));

    // التحقق من أن الملف ليس قديماً جداً (أكثر من 5 دقائق)
    const timeDiff = Date.now() - restartInfo.timestamp;
    if (timeDiff > 5 * 60 * 1000) { // 5 دقائق
      try {
        if (fs.existsSync(restartFile)) {
          fs.unlinkSync(restartFile); // حذف الملف القديم
        }
      } catch (error) {
        console.error("Error deleting old restart file:", error);
      }
      return;
    }

    // التحقق من أن البوت الحالي هو نفس البوت الذي أرسل الرسالة
    if (restartInfo.botId && restartInfo.botId !== music_client.user.id) {
      // هذا البوت لم يرسل رسالة إعادة التشغيل، تجاهل
      return;
    }

    // محاولة الحصول على القناة والرسالة
    const channel = await music_client.channels.fetch(restartInfo.channelId).catch(() => null);
    if (!channel) {
      try {
        if (fs.existsSync(restartFile)) {
          fs.unlinkSync(restartFile);
        }
      } catch (error) {
        console.error("Error deleting restart file:", error);
      }
      return;
    }

    const message = await channel.messages.fetch(restartInfo.messageId).catch(() => null);
    if (!message) {
      try {
        if (fs.existsSync(restartFile)) {
          fs.unlinkSync(restartFile);
        }
      } catch (error) {
        console.error("Error deleting restart file:", error);
      }
      return;
    }

    // التحقق من أن البوت هو مؤلف الرسالة
    if (message.author.id !== music_client.user.id) {
      console.log("Message was not authored by this bot, skipping update");
      try {
        if (fs.existsSync(restartFile)) {
          fs.unlinkSync(restartFile);
        }
      } catch (error) {
        console.error("Error deleting restart file:", error);
      }
      return;
    }

    // تحديث الرسالة برسالة النجاح
    const successEmbed = new Discord.EmbedBuilder()
      .setColor("Green")
      .setTitle("تمت إعادة التشغيل بنجاح")
      .setDescription("تمت إعادة تشغيل البوتات بنجاح. قد يستغرق الأمر بضع ثوانٍ حتى تعود البوتات للعمل بشكل كامل.");

    await message.edit({ embeds: [successEmbed], components: [] }).catch(error => {
      console.error("Error updating restart message:", error);
    });

    // حذف الملف بعد التحديث الناجح
    try {
      if (fs.existsSync(restartFile)) {
        fs.unlinkSync(restartFile);
        console.log("Successfully updated restart message and cleaned up temp file");
      }
    } catch (unlinkError) {
      console.error("Error deleting restart file:", unlinkError);
    }

  } catch (error) {
    console.error("Error in checkRestartMessage:", error);

    // محاولة حذف الملف في حالة الخطأ
    try {
      const fs = require('fs');
      const path = require('path');
      const restartFile = path.join(__dirname, 'temp_restart_info.json');
      if (fs.existsSync(restartFile)) {
        fs.unlinkSync(restartFile);
      }
    } catch (cleanupError) {
      console.error("Error cleaning up restart file:", cleanupError);
    }
  }
}

// هذه الدالة غير مستخدمة وتم استبدالها بدالة createProgressBar

// دالة محسنة لإنشاء شريط تقدم دقيق جداً في التحرك
function createProgressBar(currentTime, duration) {
  // طول شريط التقدم (أطول للدقة أكثر)
  const barLength = 20;

  // حساب نسبة التقدم بدقة عالية
  const progress = Math.min(Math.max(currentTime / duration, 0), 1);

  // حساب الموضع الدقيق للمؤشر (بدقة عشرية)
  const exactPosition = progress * barLength;
  const filledLength = Math.floor(exactPosition);
  const partialProgress = exactPosition - filledLength;

  // رموز الشريط
  const emptyChar = "─";
  const filledChar = "━";

  // مؤشرات متدرجة للحركة الناعمة
  const indicators = ["🔴", "🟠", "🟡", "🟢", "🔵"];
  const indicatorIndex = Math.floor(partialProgress * indicators.length);
  const indicatorChar = indicators[indicatorIndex] || "🔴";

  // إنشاء الشريط مع حركة ناعمة
  let bar = "";

  for (let i = 0; i < barLength; i++) {
    if (i < filledLength) {
      bar += filledChar;
    } else if (i === filledLength && progress < 1) {
      bar += indicatorChar;
    } else {
      bar += emptyChar;
    }
  }

  // تنسيق الوقت بدقة الثواني
  const currentTimeFormatted = formatTimeAccurate(currentTime);
  const durationFormatted = formatTimeAccurate(duration);

  // إضافة نسبة التقدم المئوية
  const progressPercentage = Math.round(progress * 100);

  return `▶️ ${bar} ${currentTimeFormatted}/${durationFormatted} (${progressPercentage}%)`;
}

// دالة تنسيق الوقت بدقة عالية
function formatTimeAccurate(timeInSeconds) {
  if (!timeInSeconds || timeInSeconds < 0) return "0:00";

  const totalSeconds = Math.floor(timeInSeconds);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

function formatTime(time) {
  try {
    let date = new Date(time).toString().split(" ")[4];
    if (!date) {
      console.error("Invalid time format:", time);
      return "00:00";
    }
    date = date.split(":").length >= 3 ? parseInt(date.split(":")[0]) > 0 ? date : date.split(":").slice(1).join(":") : date;
    return date;
  } catch (error) {
    console.error("Error formatting time:", error);
    return "00:00";
  }
}

// دالة لتحويل الوقت بالميلي ثانية إلى صيغة مقروءة
function prettyMilliseconds(ms) {
  if (ms === 0) return '0:00';

  const seconds = Math.floor((ms / 1000) % 60);
  const minutes = Math.floor((ms / (1000 * 60)) % 60);
  const hours = Math.floor(ms / (1000 * 60 * 60));

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}



// دالة لتغيير مستوى الصوت بشكل تدريجي
async function changeVolumeGradually(queue, startVolume, targetVolume, steps = 5, delay = 100) {
  if (!queue) return;

  // حساب حجم الخطوة
  const volumeStep = (targetVolume - startVolume) / steps;

  // تغيير مستوى الصوت تدريجياً
  for (let i = 1; i <= steps; i++) {
    const currentVolume = Math.round(startVolume + (volumeStep * i));
    try {
      queue.setVolume(currentVolume);
    } catch (error) {
      console.error("Error changing volume:", error);
    }

    // انتظار قبل الخطوة التالية
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  // التأكد من أن مستوى الصوت النهائي هو المطلوب بالضبط
  try {
    queue.setVolume(targetVolume);
  } catch (error) {
    console.error("Error setting final volume:", error);
  }
}

// دوال مساعدة لإعدادات البوت
async function handleAddOwner(interaction, music_client, data) {
  // إنشاء Modal لإدخال معرف المستخدم
  const modal = new Discord.ModalBuilder()
    .setCustomId('add_owner_modal')
    .setTitle('إضافة مالك لجميع البوتات');

  const userIdInput = new Discord.TextInputBuilder()
    .setCustomId('user_id')
    .setLabel('معرف المستخدم أو منشن')
    .setPlaceholder('123456789012345678 أو @username')
    .setStyle(Discord.TextInputStyle.Short)
    .setRequired(true);

  const userRow = new Discord.ActionRowBuilder().addComponents(userIdInput);
  modal.addComponents(userRow);

  await interaction.showModal(modal);

  // انتظار إرسال Modal
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: 60000 });
    let userId = modalSubmit.fields.getTextInputValue('user_id').trim();

    // إزالة المنشن إذا كان موجوداً
    userId = userId.replace(/[<@!>]/g, '');

    // التحقق من صحة معرف المستخدم
    if (!userId || isNaN(userId) || userId.length < 15) {
      return await modalSubmit.reply({
        content: "❌ معرف المستخدم غير صالح. يجب أن يكون مكون من 15-20 رقم.",
        ephemeral: true
      });
    }

    // محاولة جلب المستخدم
    let user;
    try {
      user = await music_client.users.fetch(userId);
    } catch (error) {
      return await modalSubmit.reply({
        content: "❌ لم يتم العثور على مستخدم بهذا المعرف.",
        ephemeral: true
      });
    }

    await modalSubmit.deferReply({ flags: MessageFlags.Ephemeral });

    // التحقق من أن المستخدم ليس مالكاً بالفعل
    if (data.owners.find(o => o === user.id)) {
      return await modalSubmit.editReply({
        content: `❌ المستخدم ${user.username} مالك بالفعل.`
      });
    }

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database("databases/subscriptions.json");

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // إضافة المالك لجميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // إضافة المالك باستخدام musicBotService
          await musicBotService.addOwner(bot.botId, user.id);

          successCount++;
          results.push(`✅ ${bot.botId}: تم إضافة المالك`);

        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في الإضافة`);
          console.error(`Error adding owner to bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم إضافة المالك للبوتات`)
        .setDescription(`تم إضافة ${user.username} كمالك لـ ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "المستخدم المضاف",
            value: `<@${user.id}> (${user.username})`,
            inline: true
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error adding owner to all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("فشل في إضافة المالك")
        .setDescription("حدث خطأ أثناء إضافة المالك لجميع البوتات")
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in add owner modal:', error);
    if (error.code === 'InteractionCollectorError') {
      // تجاهل خطأ timeout بصمت - لا نرسل رسالة
      console.log('Modal timeout - user did not submit in time');
    } else {
      // فقط في حالة الأخطاء الأخرى نرسل رسالة
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.followUp({
            content: "حدث خطأ أثناء معالجة طلبك.",
            ephemeral: true
          });
        }
      } catch (followUpError) {
        console.error("Error sending error followUp:", followUpError);
      }
    }
  }
}

async function handleRemoveOwner(interaction, music_client, data) {
  if (data.owners.length === 0) {
    return await interaction.reply({
      content: "❌ لا يوجد مالكين لإزالتهم.",
      ephemeral: true
    });
  }

  // إنشاء قائمة بالمالكين الحاليين
  const ownerOptions = [];
  for (let ownerId of data.owners) {
    try {
      const user = await music_client.users.fetch(ownerId);
      ownerOptions.push({
        label: user.username,
        description: `معرف: ${ownerId}`,
        value: ownerId
      });
    } catch (error) {
      // إذا لم يتم العثور على المستخدم، أضفه بمعرفه فقط
      ownerOptions.push({
        label: `مستخدم غير معروف`,
        description: `معرف: ${ownerId}`,
        value: ownerId
      });
    }
  }

  const removeMenu = new Discord.StringSelectMenuBuilder()
    .setCustomId('remove_owner_menu')
    .setPlaceholder('اختر مالكاً لإزالته من جميع البوتات')
    .addOptions(ownerOptions);

  const removeEmbed = new Discord.EmbedBuilder()
    .setColor("Orange")
    .setTitle("إزالة مالك من جميع البوتات")
    .setDescription("اختر المالك الذي تريد إزالته من جميع البوتات")
    .addFields([
      {
        name: "المالكين الحاليين",
        value: data.owners.map(ownerId => `<@${ownerId}>`).join(', '),
        inline: false
      }
    ])
    .setFooter({ text: "اختر من القائمة أدناه" });

  const removeRow = new Discord.ActionRowBuilder().addComponents(removeMenu);

  await interaction.reply({ embeds: [removeEmbed], components: [removeRow], flags: MessageFlags.Ephemeral });

  // انتظار اختيار المستخدم
  const result = await safeAwaitMessageComponent(interaction.channel, {
    filter: i => i.user.id === interaction.user.id && i.customId === 'remove_owner_menu',
    time: 60000
  });

  if (!result.success) {
    if (result.error === 'timeout') {
      return await safeErrorReply(interaction, "انتهت مهلة اختيار المالك.", true);
    }
    return await safeErrorReply(interaction, `❌ حدث خطأ: ${result.message}`, true);
  }

  const selectInteraction = result.interaction;

    const ownerIdToRemove = selectInteraction.values[0];

    await safeDeferReply(selectInteraction, true);

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database("databases/subscriptions.json");

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // إزالة المالك من جميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // إزالة المالك باستخدام musicBotService
          await musicBotService.removeOwner(bot.botId, ownerIdToRemove);

          successCount++;
          results.push(`✅ ${bot.botId}: تم إزالة المالك`);

        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في الإزالة`);
          console.error(`Error removing owner from bot ${bot.botId}:`, error);
        }
      }

      let removedUser;
      try {
        removedUser = await music_client.users.fetch(ownerIdToRemove);
      } catch (error) {
        removedUser = { username: "مستخدم غير معروف", id: ownerIdToRemove };
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم إزالة المالك من البوتات`)
        .setDescription(`تم إزالة ${removedUser.username} من ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "المستخدم المُزال",
            value: `<@${ownerIdToRemove}> (${removedUser.username})`,
            inline: true
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          }
        ])
        .setTimestamp();

      await selectInteraction.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error removing owner from all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في إزالة المالك")
        .setDescription("حدث خطأ أثناء إزالة المالك من جميع البوتات")
        .setTimestamp();

      await selectInteraction.editReply({ embeds: [errorEmbed] });
    }


}

async function handleListOwners(interaction, music_client, data) {
  if (data.owners.length === 0) {
    const noOwnersEmbed = new Discord.EmbedBuilder()
      .setColor("Orange")
      .setTitle("قائمة المالكين")
      .setDescription("لا يوجد مالكين مضافين حالياً.")
      .setFooter({ text: "استخدم خيار 'إضافة مالك' لإضافة مالكين جدد" })
      .setTimestamp();

    return await interaction.reply({ embeds: [noOwnersEmbed], ephemeral: true });
  }

  // إنشاء قائمة المالكين
  const ownersList = [];
  for (let i = 0; i < data.owners.length; i++) {
    const ownerId = data.owners[i];
    try {
      const user = await music_client.users.fetch(ownerId);
      ownersList.push(`${i + 1}. <@${ownerId}> (${user.username})`);
    } catch (error) {
      ownersList.push(`${i + 1}. <@${ownerId}> (مستخدم غير معروف)`);
    }
  }

  const ownersEmbed = new Discord.EmbedBuilder()
    .setColor("Blue")
    .setTitle("قائمة المالكين")
    .setDescription(ownersList.join("\n"))
    .addFields([
      {
        name: "إجمالي المالكين",
        value: `${data.owners.length} مالك`,
        inline: true
      },
      {
        name: "البوت",
        value: `${music_client.user.username}`,
        inline: true
      }
    ])
    .setTimestamp();

  await interaction.reply({ embeds: [ownersEmbed], flags: MessageFlags.Ephemeral });
}

async function handleChangeAvatars(interaction, music_client) {
  // إنشاء Modal لإدخال رابط الصورة
  const modal = new Discord.ModalBuilder()
    .setCustomId('change_avatars_modal')
    .setTitle('تغيير صور جميع البوتات');

  const avatarUrlInput = new Discord.TextInputBuilder()
    .setCustomId('avatar_url')
    .setLabel('رابط الصورة الجديدة لجميع البوتات')
    .setPlaceholder('https://example.com/image.png')
    .setStyle(Discord.TextInputStyle.Short)
    .setRequired(true);

  const avatarRow = new Discord.ActionRowBuilder().addComponents(avatarUrlInput);
  modal.addComponents(avatarRow);

  await interaction.showModal(modal);

  // انتظار إرسال Modal
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: 60000 });
    const avatarUrl = modalSubmit.fields.getTextInputValue('avatar_url').trim();

    // التحقق من صحة الرابط
    if (!avatarUrl || !avatarUrl.startsWith('http')) {
      return await modalSubmit.reply({
        content: "❌ رابط الصورة غير صالح. يجب أن يبدأ بـ http أو https.",
        ephemeral: true
      });
    }

    await modalSubmit.deferReply({ ephemeral: true });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database("databases/subscriptions.json");

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // تغيير صورة جميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // إنشاء عميل مؤقت للبوت
          const { Client, GatewayIntentBits } = require('discord.js');
          const tempClient = new Client({
            intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildVoiceStates, GatewayIntentBits.GuildMessages]
          });

          await tempClient.login(bot.botToken);
          await tempClient.user.setAvatar(avatarUrl);
          await tempClient.destroy();

          successCount++;
          results.push(`✅ ${tempClient.user?.username || bot.botId}: نجح`);
        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل`);
          console.error(`Error changing avatar for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم تغيير صور البوتات`)
        .setDescription(`تم تحديث صور ${successCount} من ${subscription.bots.length} بوت`)
        .setThumbnail(avatarUrl)
        .addFields([
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error changing avatars:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في تغيير الصور")
        .setDescription("حدث خطأ أثناء تغيير صور البوتات")
        .setFooter({ text: "تأكد من أن الرابط يؤدي إلى صورة صالحة" })
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in change avatars modal:', error);
    if (error.code === 'InteractionCollectorError') {
      // تجاهل خطأ timeout بصمت - لا نرسل رسالة
      console.log('Modal timeout - user did not submit in time');
    } else {
      // فقط في حالة الأخطاء الأخرى نرسل رسالة
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.followUp({
            content: "حدث خطأ أثناء معالجة طلبك.",
            ephemeral: true
          });
        }
      } catch (followUpError) {
        console.error("Error sending error followUp:", followUpError);
      }
    }
  }
}

async function handleChangeNames(interaction, music_client) {
  // إنشاء Modal لإدخال الاسم الجديد
  const modal = new Discord.ModalBuilder()
    .setCustomId('change_names_modal')
    .setTitle('تغيير أسماء جميع البوتات');

  const nameInput = new Discord.TextInputBuilder()
    .setCustomId('bot_name')
    .setLabel('الاسم الجديد لجميع البوتات')
    .setPlaceholder('اسم البوتات الجديد')
    .setStyle(Discord.TextInputStyle.Short)
    .setRequired(true)
    .setMaxLength(32);

  const nameRow = new Discord.ActionRowBuilder().addComponents(nameInput);
  modal.addComponents(nameRow);

  await interaction.showModal(modal);

  // انتظار إرسال Modal
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: 60000 });
    const newName = modalSubmit.fields.getTextInputValue('bot_name').trim();

    // التحقق من صحة الاسم
    if (!newName || newName.length < 2) {
      return await modalSubmit.reply({
        content: "❌ الاسم غير صالح. يجب أن يكون على الأقل حرفين.",
        ephemeral: true
      });
    }

    await modalSubmit.deferReply({ ephemeral: true });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database("databases/subscriptions.json");

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // تغيير أسماء جميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let i = 0; i < subscription.bots.length; i++) {
        let bot = subscription.bots[i];
        try {
          // إنشاء عميل مؤقت للبوت
          const { Client, GatewayIntentBits } = require('discord.js');
          const tempClient = new Client({
            intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildVoiceStates, GatewayIntentBits.GuildMessages]
          });

          await tempClient.login(bot.botToken);
          const oldName = tempClient.user.username;

          // إضافة رقم للاسم إذا كان هناك أكثر من بوت
          const finalName = subscription.bots.length > 1 ? `${newName} ${i + 1}` : newName;

          await tempClient.user.setUsername(finalName);
          await tempClient.destroy();

          successCount++;
          results.push(`✅ ${oldName} → ${finalName}: نجح`);
        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل`);
          console.error(`Error changing name for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم تغيير أسماء البوتات`)
        .setDescription(`تم تحديث أسماء ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "النتائج",
            value: results.join("\n") || "لا توجد نتائج",
            inline: false
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error changing names:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في تغيير الأسماء")
        .setDescription("حدث خطأ أثناء تغيير أسماء البوتات")
        .setFooter({ text: "يمكن تغيير اسم البوت مرتين فقط في الساعة" })
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in change names modal:', error);
    if (error.code === 'InteractionCollectorError') {
      // تجاهل خطأ timeout بصمت - لا نرسل رسالة
      console.log('Modal timeout - user did not submit in time');
    } else {
      // فقط في حالة الأخطاء الأخرى نرسل رسالة
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.followUp({
            content: "حدث خطأ أثناء معالجة طلبك.",
            ephemeral: true
          });
        }
      } catch (followUpError) {
        console.error("Error sending error followUp:", followUpError);
      }
    }
  }
}



async function handleSetGameAll(interaction, music_client) {
  // إنشاء Modal لإدخال حالة النشاط
  const modal = new Discord.ModalBuilder()
    .setCustomId('set_game_all_modal')
    .setTitle('تغيير حالة جميع البوتات');

  const gameInput = new Discord.TextInputBuilder()
    .setCustomId('game_text')
    .setLabel('النشاط/الحالة الجديدة')
    .setPlaceholder('مثال: يستمع إلى الموسيقى')
    .setStyle(Discord.TextInputStyle.Short)
    .setRequired(true)
    .setMaxLength(128);

  const gameRow = new Discord.ActionRowBuilder().addComponents(gameInput);
  modal.addComponents(gameRow);

  await interaction.showModal(modal);

  // انتظار إرسال Modal
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: 60000 });
    const gameText = modalSubmit.fields.getTextInputValue('game_text').trim();

    // التحقق من صحة النص
    if (!gameText || gameText.length < 1) {
      return await modalSubmit.reply({
        content: "❌ نص الحالة غير صالح. يجب أن يحتوي على حرف واحد على الأقل.",
        ephemeral: true
      });
    }

    await modalSubmit.deferReply({ ephemeral: true });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database("databases/subscriptions.json");

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // حفظ حالة النشاط في قاعدة البيانات لجميع البوتات
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // تحديث حالة النشاط باستخدام musicBotService
          await musicBotService.updateField(bot.botId, 'game', gameText);

          successCount++;
          results.push(`✅ ${bot.botId}: تم حفظ الحالة`);
        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في الحفظ`);
          console.error(`Error saving game for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم حفظ حالة البوتات`)
        .setDescription(`تم حفظ حالة ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "الحالة الجديدة",
            value: `${gameText}`,
            inline: true
          }
        ])
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error setting game for all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في تغيير الحالة")
        .setDescription("حدث خطأ أثناء تغيير حالة البوتات")
        .setTimestamp();

      await modalSubmit.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in set game all modal:', error);
    if (error.code === 'InteractionCollectorError') {
      // تجاهل خطأ timeout بصمت - لا نرسل رسالة
      console.log('Modal timeout - user did not submit in time');
    } else {
      // فقط في حالة الأخطاء الأخرى نرسل رسالة
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.followUp({
            content: "حدث خطأ أثناء معالجة طلبك.",
            ephemeral: true
          });
        }
      } catch (followUpError) {
        console.error("Error sending error followUp:", followUpError);
      }
    }
  }
}

async function handleSetChat(interaction, music_client) {
  try {
    // الحصول على الشات الحالي
    const currentChannel = interaction.channel;

    await interaction.deferReply({ ephemeral: true });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database("databases/subscriptions.json");

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // تحديد شات الأوامر لجميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // تحديث شات الأوامر باستخدام musicBotService
          await musicBotService.updateField(bot.botId, 'commandsChat', currentChannel.id);

          // تحديث المتغير العام للبوت الحالي
          if (bot.botId === music_client.user.id) {
            channelId = currentChannel.id;
            console.log(`Updated channelId for current bot ${music_client.user.id} to ${currentChannel.id}`);
          }

          successCount++;
          results.push(`✅ ${bot.botId}: تم تحديد الشات`);

        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في التحديد`);
          console.error(`Error setting chat for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم تحديد شات الأوامر`)
        .setDescription(`تم تحديد شات الأوامر لـ ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "الشات المحدد",
            value: `<#${currentChannel.id}> (${currentChannel.name})`,
            inline: true
          },
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          }
        ])
        .setTimestamp();

      await interaction.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error setting chat for all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في تحديد شات الأوامر")
        .setDescription("حدث خطأ أثناء تحديد شات الأوامر للبوتات")
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in set chat:', error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "❌ حدث خطأ أثناء معالجة طلبك.",
        ephemeral: true
      }).catch(() => {});
    }
  }
}

async function handleDisChat(interaction, music_client) {
  try {
    await interaction.deferReply({ ephemeral: true });

    try {
      // الحصول على معلومات الاشتراك
      const { Database } = require('st.db');
      const sub_db = new Database("databases/subscriptions.json");

      let sub_data = await sub_db.get(process.env.owner_id);
      if (!sub_data || !Array.isArray(sub_data)) {
        throw new Error("No subscription data found");
      }

      let subscription = sub_data.find((s) => s.id == process.env.subscription_id);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // إلغاء تحديد شات الأوامر لجميع البوتات في الاشتراك
      let successCount = 0;
      let failCount = 0;
      let results = [];

      for (let bot of subscription.bots) {
        try {
          // إلغاء تحديد شات الأوامر باستخدام musicBotService
          await musicBotService.updateField(bot.botId, 'commandsChat', null);

          // تحديث المتغير العام للبوت الحالي
          if (bot.botId === music_client.user.id) {
            channelId = null;
            console.log(`Updated channelId for current bot ${music_client.user.id} to null (disabled)`);
          }

          successCount++;
          results.push(`✅ ${bot.botId}: تم إلغاء التحديد`);

        } catch (error) {
          failCount++;
          results.push(`❌ ${bot.botId}: فشل في الإلغاء`);
          console.error(`Error removing chat for bot ${bot.botId}:`, error);
        }
      }

      const successEmbed = new Discord.EmbedBuilder()
        .setColor(failCount === 0 ? "Green" : "Orange")
        .setTitle(`${failCount === 0 ? "✅" : "⚠️"} تم إلغاء تحديد شات الأوامر`)
        .setDescription(`تم إلغاء تحديد شات الأوامر لـ ${successCount} من ${subscription.bots.length} بوت`)
        .addFields([
          {
            name: "الإحصائيات",
            value: `نجح: ${successCount}\nفشل: ${failCount}\nالإجمالي: ${subscription.bots.length}`,
            inline: true
          }
          ])
        .setTimestamp();

      await interaction.editReply({ embeds: [successEmbed] });

    } catch (error) {
      console.error('Error removing chat for all bots:', error);

      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ فشل في إلغاء تحديد شات الأوامر")
        .setDescription("حدث خطأ أثناء إلغاء تحديد شات الأوامر للبوتات")
        .setTimestamp();

      await interaction.editReply({ embeds: [errorEmbed] });
    }

  } catch (error) {
    console.error('Error in dis chat:', error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "❌ حدث خطأ أثناء معالجة طلبك.",
        ephemeral: true
      }).catch(() => {});
    }
  }
}



// تصدير الدوال للاستخدام في ملفات أخرى
module.exports = {
  handleChangeAvatars,
  handleChangeNames,
  handleSetGameAll
};

